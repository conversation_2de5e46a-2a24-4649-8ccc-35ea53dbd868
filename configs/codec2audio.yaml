---
sample_rate: 44100
clip_duration: 2.
target_stems: ["vocals"]

train_datasets:
    MUSDB18HQ:
        root: "./datasets/musdb18hq"
        split: "train"
        time_align: strict

test_datasets:
    MUSDB18HQ:
        root: "./datasets/musdb18hq"
        split: "test"
        time_align: strict

sampler: InfiniteSampler

data_transform: 
    name: Codec2Audio_Mel

model:
    name: BSRoformerMel
    y_dim:  # Leave blank if None
    c_dim:  # Leave blank if None
    ct_dim:  # Leave blank if None
    ctf_dim: 1
    cx_dim:  # Leave blank if None

    in_channels: 2
    patch_size: [4, 4]
    n_layer: 12
    n_head: 12
    n_embd: 384

train:
    device: cuda
    num_workers: 16
    precision: "no"  # "no" (fp32) | "fp8" | "fp16" | bf16 
    loss: l1
    optimizer: AdamW
    lr: 1e-4
    warm_up_steps: 1000  # Leave blank if no warm up is used
    batch_size_per_device: 4
    test_every_n_steps: 2000
    save_every_n_steps: 20000
    training_steps: 200000
    resume_ckpt_path:  # Leave blank if train from scratch

valid_audios: 2