---
sample_rate: 44100
clip_duration: 2.

train_datasets:
    MAESTRO:
        root: "./datasets/maestro-v3.0.0"
        split: "train"

test_datasets:
    MAESTRO:
        root: "./datasets/maestro-v3.0.0"
        split: "test"

sampler: InfiniteSampler

data_transform: 
    name: Midi2Audio_Mel

model:
    name: BSRoformerMel
    y_dim:  # Leave blank if None
    c_dim:  # Leave blank if None
    ct_dim:  128
    ctf_dim: # Leave blank if None
    cx_dim:  # Leave blank if None

    in_channels: 2
    patch_size: [4, 4]
    n_layer: 12
    n_head: 12
    n_embd: 384

train:
    device: cuda
    num_workers: 16
    precision: "no"  # "no" (fp32) | "fp8" | "fp16" | bf16 
    loss: l1
    optimizer: AdamW
    lr: 1e-4
    warm_up_steps: 1000  # Leave blank if no warm up is used
    batch_size_per_device: 4
    test_every_n_steps: 2000
    save_every_n_steps: 20000
    training_steps: 200000
    resume_ckpt_path:  # Leave blank if train from scratch

valid_audios: 2