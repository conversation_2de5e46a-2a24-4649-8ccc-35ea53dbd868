{"name": "audio_flow", "protocol": "sftp", "uploadOnSave": true, "useTempFile": true, "context": "./", "ignore": [".vscode", ".git", ".DS_Store", ".github/**", ".ci", "**/*.pyc", "**/__pycache__", "**/__pycache__/**"], "watcher": {"files": "statics/**/*", "autoUpload": false, "autoDelete": false}, "remoteExplorer": {"filesExclude": [".git", ".vscode", ".github", "**/*.pyc", "**/__pycache__", "**/__pycache__/**", "./data"]}, "profiles": {"aixp": {"host": "aixp", "port": 22, "username": "yincao", "password": "c8018503598", "privateKeyPath": "/Users/<USER>/.ssh/id_aixp", "remotePath": "/gpfs/home/<USER>/yincao/Workspace/codes/generative_models/audio_flow"}, "aixppgy": {"host": "amaxpgy", "port": 22, "username": "yincao", "privateKeyPath": "/Users/<USER>/.ssh/id_amax", "remotePath": "/home/<USER>/Mount/aixp/codes/generative_models/audio_flow"}, "amax": {"host": "amax", "port": 22, "username": "yincao", "privateKeyPath": "/Users/<USER>/.ssh/id_amax", "remotePath": "/home/<USER>/Workspace/codes/generative_models/audio_flow"}, "amaxpgy": {"host": "amaxpgy", "port": 22, "username": "yincao", "privateKeyPath": "/Users/<USER>/.ssh/id_amax", "remotePath": "/home/<USER>/Workspace/codes/generative_models/audio_flow"}, "bofur": {"host": "bofur", "port": 22, "username": "yincao", "privateKeyPath": "/Users/<USER>/.ssh/id_bofur", "remotePath": "/home/<USER>/Workspace/codes/generative_models/audio_flow"}, "bofurpgy": {"host": "bofurpgy", "port": 22, "username": "yincao", "privateKeyPath": "/Users/<USER>/.ssh/id_bofur", "remotePath": "/home/<USER>/Workspace/codes/generative_models/audio_flow"}}, "defaultProfile": "bofurpgy"}